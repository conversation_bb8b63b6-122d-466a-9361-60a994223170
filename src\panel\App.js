import React, { useState, useEffect, useRef } from 'react';
import { Settings, X, Plus, Search } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import SearchBar from './components/SearchBar';
import PromptList from './components/PromptList';
import PromptPreview from './components/PromptPreview';
import SettingsPanel from './components/SettingsPanel';
import PromptEditor from './components/PromptEditor';
import FolderDropZone from './components/FolderDropZone';
import { cn } from '../lib/utils';

function App() {
  const [prompts, setPrompts] = useState([]);
  const [folders, setFolders] = useState([]);
  const [filteredPrompts, setFilteredPrompts] = useState([]);
  const [selectedPromptIndex, setSelectedPromptIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState(null);
  const searchInputRef = useRef(null);

  // 初始化数据
  useEffect(() => {
    loadData();
    
    // 监听来自content script的消息
    window.addEventListener('message', handleContentMessage);
    
    return () => {
      window.removeEventListener('message', handleContentMessage);
    };
  }, []);

  // 当面板显示时，自动聚焦搜索框
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // 处理搜索
  useEffect(() => {
    filterPrompts(searchQuery);
  }, [prompts, searchQuery]);

  // 重置选中索引当过滤结果改变时
  useEffect(() => {
    setSelectedPromptIndex(0);
  }, [filteredPrompts]);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      
      // 获取prompts
      const promptsResponse = await sendMessageToBackground({ action: 'get-prompts' });
      const foldersResponse = await sendMessageToBackground({ action: 'get-folders' });
      
      setPrompts(promptsResponse || []);
      setFolders(foldersResponse || []);
      
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 向background script发送消息
  const sendMessageToBackground = (message) => {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  };

  // 处理来自content script的消息
  const handleContentMessage = (event) => {
    
    switch (event.data.action) {
      case 'panel-shown':
        // 面板显示时，聚焦搜索框
        if (searchInputRef.current) {
          setTimeout(() => {
            searchInputRef.current.focus();
          }, 100);
        }
        break;
        
      default:
        break;
    }
  };

  // 过滤prompts
  const filterPrompts = (query) => {
    let filtered = [...prompts];

    // 按order字段排序，如果没有order字段则按创建时间排序
    filtered.sort((a, b) => {
      const orderA = typeof a.order === 'number' ? a.order : a.createdAt || 0;
      const orderB = typeof b.order === 'number' ? b.order : b.createdAt || 0;
      return orderA - orderB;
    });

    if (!query.trim()) {
      setFilteredPrompts(filtered);
      return;
    }

    const lowerQuery = query.toLowerCase();
    filtered = filtered.filter(prompt => {
      // 搜索标题、描述、内容
      const titleMatch = prompt.title.toLowerCase().includes(lowerQuery);
      const descriptionMatch = prompt.description?.toLowerCase().includes(lowerQuery);
      const contentMatch = prompt.content.toLowerCase().includes(lowerQuery);

      // 搜索文件夹名称
      let folderMatch = false;
      if (prompt.folderId) {
        const folder = folders.find(f => f.id === prompt.folderId);
        if (folder) {
          folderMatch = folder.name.toLowerCase().includes(lowerQuery);
        }
      }

      return titleMatch || descriptionMatch || contentMatch || folderMatch;
    });

    setFilteredPrompts(filtered);
  };

  // 处理键盘导航
  const handleKeyDown = (event) => {
    if (filteredPrompts.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedPromptIndex(prev => 
          prev < filteredPrompts.length - 1 ? prev + 1 : 0
        );
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        setSelectedPromptIndex(prev => 
          prev > 0 ? prev - 1 : filteredPrompts.length - 1
        );
        break;
        
      case 'Enter':
        event.preventDefault();
        if (filteredPrompts[selectedPromptIndex]) {
          insertPrompt(filteredPrompts[selectedPromptIndex]);
        }
        break;
        
      case 'Escape':
        event.preventDefault();
        hidePanel();
        break;
        
      default:
        break;
    }
  };

  // 插入prompt到页面
  const insertPrompt = (prompt) => {
    
    // 向content script发送消息
    window.parent.postMessage({
      action: 'insert-prompt',
      content: prompt.content
    }, '*');
    
    // 更新使用次数
    updatePromptUsage(prompt.id);
  };

  // 更新prompt使用次数
  const updatePromptUsage = async (promptId) => {
    try {
      const prompt = prompts.find(p => p.id === promptId);
      if (prompt) {
        const updatedPrompt = {
          ...prompt,
          usageCount: (prompt.usageCount || 0) + 1,
          lastUsedAt: Date.now()
        };
        
        await sendMessageToBackground({
          action: 'save-prompt',
          prompt: updatedPrompt
        });
        
        // 更新本地状态
        setPrompts(prev => prev.map(p => 
          p.id === promptId ? updatedPrompt : p
        ));
      }
    } catch (error) {
      console.error('Error updating prompt usage:', error);
    }
  };

  // 隐藏面板
  const hidePanel = () => {
    window.parent.postMessage({
      action: 'hide-panel'
    }, '*');
  };

  // 获取文件夹名称
  const getFolderName = (folderId) => {
    if (!folderId) return null;
    const folder = folders.find(f => f.id === folderId);
    return folder ? folder.name : null;
  };

  // 处理数据导入后的刷新
  const handleDataImported = () => {
    loadData();
  };

  // 显示设置面板
  const showSettingsPanel = () => {
    setShowSettings(true);
  };

  // 隐藏设置面板
  const hideSettingsPanel = () => {
    setShowSettings(false);
  };

  // 显示添加Prompt编辑器
  const showAddPromptEditor = () => {
    setEditingPrompt(null);
    setShowEditor(true);
  };

  // 显示编辑Prompt编辑器
  const showEditPromptEditor = (prompt) => {
    setEditingPrompt(prompt);
    setShowEditor(true);
  };

  // 隐藏编辑器
  const hideEditor = () => {
    setShowEditor(false);
    setEditingPrompt(null);
  };

  // 保存Prompt
  const savePrompt = async (promptData) => {
    try {
      const response = await sendMessageToBackground({
        action: 'save-prompt',
        prompt: promptData
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
        hideEditor();
      } else {
        throw new Error(response.error || '保存失败');
      }
    } catch (error) {
      console.error('Error saving prompt:', error);
      throw error;
    }
  };

  // 删除Prompt
  const deletePrompt = async (promptId) => {
    try {
      const response = await sendMessageToBackground({
        action: 'delete-prompt',
        promptId
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
        hideEditor();
      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error) {
      console.error('Error deleting prompt:', error);
      throw error;
    }
  };

  // 处理Prompt移动（拖拽排序）
  const handlePromptMove = async (draggedPrompt, targetPrompt) => {
    try {
      // 获取当前显示的prompts列表
      const currentPrompts = [...filteredPrompts];

      // 找到拖拽的prompt和目标prompt的索引
      const draggedIndex = currentPrompts.findIndex(p => p.id === draggedPrompt.id);
      const targetIndex = currentPrompts.findIndex(p => p.id === targetPrompt.id);

      if (draggedIndex === -1 || targetIndex === -1) return;

      // 重新排列数组
      const newPrompts = [...currentPrompts];
      const [draggedItem] = newPrompts.splice(draggedIndex, 1);
      newPrompts.splice(targetIndex, 0, draggedItem);

      // 生成新的排序ID列表
      const promptIds = newPrompts.map(p => p.id);

      // 发送排序请求到background
      const response = await sendMessageToBackground({
        action: 'reorder-prompts',
        promptIds
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
      } else {
        throw new Error(response.error || '排序失败');
      }

    } catch (error) {
      console.error('Error moving prompt:', error);
    }
  };

  // 处理Prompt移动到文件夹
  const handlePromptMoveToFolder = async (promptId, folderId) => {
    try {
      const response = await sendMessageToBackground({
        action: 'move-prompt-to-folder',
        promptId,
        folderId
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
      } else {
        throw new Error(response.error || '移动失败');
      }
    } catch (error) {
      console.error('Error moving prompt to folder:', error);
    }
  };

  // 处理文件夹重排序
  const handleFolderReorder = async (draggedFolderId, targetFolderId) => {
    try {
      // 获取当前文件夹列表并按order排序
      const sortedFolders = [...folders].sort((a, b) => (a.order || 0) - (b.order || 0));

      // 找到拖拽的文件夹和目标文件夹的索引
      const draggedIndex = sortedFolders.findIndex(f => f.id === draggedFolderId);
      const targetIndex = sortedFolders.findIndex(f => f.id === targetFolderId);

      if (draggedIndex === -1 || targetIndex === -1) return;

      // 重新排列数组
      const newFolders = [...sortedFolders];
      const [draggedItem] = newFolders.splice(draggedIndex, 1);
      newFolders.splice(targetIndex, 0, draggedItem);

      // 生成新的排序ID列表
      const folderIds = newFolders.map(f => f.id);

      // 发送排序请求到background
      const response = await sendMessageToBackground({
        action: 'reorder-folders',
        folderIds
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
      } else {
        throw new Error(response.error || '排序失败');
      }

    } catch (error) {
      console.error('Error reordering folders:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-muted-foreground">加载中...</div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-background text-foreground" onKeyDown={handleKeyDown} tabIndex={0}>
      {/* Header */}
      <div className="flex items-center justify-between px-5 py-4 border-b bg-muted/30">
        <h1 className="text-lg font-semibold text-foreground">Prompt 管理器</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={showSettingsPanel}
            className="h-7 w-7"
          >
            <Settings className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={hidePanel}
            className="h-7 w-7"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-5 py-4 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            ref={searchInputRef}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索 Prompt..."
            className="pl-10"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Add Button */}
        <div className="px-5 py-3 border-b bg-muted/20">
          <Button
            onClick={showAddPromptEditor}
            className="w-full justify-start gap-2"
            variant="outline"
          >
            <Plus className="h-4 w-4" />
            添加 Prompt
          </Button>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          <div className="flex-1 flex flex-col overflow-hidden">
            <PromptList
              prompts={filteredPrompts}
              selectedIndex={selectedPromptIndex}
              onSelect={setSelectedPromptIndex}
              onInsert={insertPrompt}
              getFolderName={getFolderName}
              searchQuery={searchQuery}
              onEdit={showEditPromptEditor}
              onDelete={deletePrompt}
              onPromptMove={handlePromptMove}
            />

            {filteredPrompts[selectedPromptIndex] && (
              <PromptPreview
                prompt={filteredPrompts[selectedPromptIndex]}
                folderName={getFolderName(filteredPrompts[selectedPromptIndex].folderId)}
              />
            )}
          </div>

          <div className="w-48 border-l bg-muted/30 overflow-y-auto">
            <FolderDropZone
              folders={folders}
              onPromptMoveToFolder={handlePromptMoveToFolder}
              onFolderReorder={handleFolderReorder}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t px-5 py-2 bg-muted/20">
        <div className="flex gap-4 text-xs text-muted-foreground">
          <span className="flex items-center gap-1">
            <kbd className="px-1 py-0.5 bg-muted rounded text-xs">↑↓</kbd>
            选择
          </span>
          <span className="flex items-center gap-1">
            <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Enter</kbd>
            插入
          </span>
          <span className="flex items-center gap-1">
            <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd>
            关闭
          </span>
        </div>
      </div>

      {/* Modals */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <SettingsPanel
            onClose={hideSettingsPanel}
            onDataImported={handleDataImported}
          />
        </div>
      )}

      {showEditor && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <PromptEditor
            prompt={editingPrompt}
            folders={folders}
            onSave={savePrompt}
            onCancel={hideEditor}
            onDelete={deletePrompt}
          />
        </div>
      )}
    </div>
  );
}

export default App;
