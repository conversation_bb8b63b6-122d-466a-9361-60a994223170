import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';

function PromptPreview({ prompt, folderName }) {
  if (!prompt) {
    return null;
  }

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="border-t bg-muted/20">
      <Card className="m-4 shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <CardTitle className="text-base">{prompt.title}</CardTitle>
            {folderName && (
              <Badge variant="outline" className="text-xs">
                {folderName}
              </Badge>
            )}
          </div>
          {prompt.description && (
            <CardDescription className="text-sm">
              {prompt.description}
            </CardDescription>
          )}
        </CardHeader>

        <CardContent className="pt-0 space-y-4">
          <div>
            <h4 className="text-sm font-medium text-foreground mb-2">内容预览</h4>
            <div className="bg-muted/50 rounded-md p-3 text-xs font-mono text-muted-foreground max-h-20 overflow-y-auto whitespace-pre-wrap">
              {prompt.content}
            </div>
          </div>

          <div className="space-y-2 pt-2 border-t">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">创建时间:</span>
              <span className="text-foreground font-medium">{formatDate(prompt.createdAt)}</span>
            </div>

            {prompt.updatedAt && prompt.updatedAt !== prompt.createdAt && (
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">更新时间:</span>
                <span className="text-foreground font-medium">{formatDate(prompt.updatedAt)}</span>
              </div>
            )}

            {prompt.usageCount > 0 && (
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">使用次数:</span>
                <span className="text-foreground font-medium">{prompt.usageCount}</span>
              </div>
            )}

            {prompt.lastUsedAt && (
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">最后使用:</span>
                <span className="text-foreground font-medium">{formatDate(prompt.lastUsedAt)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PromptPreview;
