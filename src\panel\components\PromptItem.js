import React, { forwardRef, useState } from 'react';
import { Edit, Trash2, GripVertical } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { cn } from '../../lib/utils';

const PromptItem = forwardRef(({
  prompt,
  isSelected,
  folderName,
  searchQuery,
  onClick,
  onDoubleClick,
  onEdit,
  onDelete,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  isDragging,
  isDragOver
}, ref) => {
  const [dragStarted, setDragStarted] = useState(false);
  
  // 高亮搜索关键词
  const highlightText = (text, query) => {
    if (!query || !text) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="highlight">{part}</mark>
      ) : part
    );
  };

  const handleClick = () => {
    onClick();
  };

  const handleDoubleClick = () => {
    onDoubleClick();
  };

  // 拖拽事件处理
  const handleDragStart = (e) => {
    setDragStarted(true);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', prompt.id);

    // 设置拖拽图像
    const dragImage = e.currentTarget.cloneNode(true);
    dragImage.style.opacity = '0.8';
    dragImage.style.transform = 'rotate(5deg)';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 0, 0);

    // 清理临时元素
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);

    if (onDragStart) {
      onDragStart(prompt, e);
    }
  };

  const handleDragEnd = (e) => {
    setDragStarted(false);
    if (onDragEnd) {
      onDragEnd(prompt, e);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    if (onDragOver) {
      onDragOver(prompt, e);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const draggedId = e.dataTransfer.getData('text/plain');
    if (onDrop && draggedId !== prompt.id) {
      onDrop(draggedId, prompt, e);
    }
  };

  return (
    <div
      ref={ref}
      className={cn(
        "group relative p-4 border-b cursor-pointer transition-all duration-200 hover:bg-muted/50",
        isSelected && "bg-primary/10 border-l-4 border-l-primary",
        isDragging && "opacity-50 rotate-1 shadow-lg z-50",
        isDragOver && "border-t-2 border-t-primary bg-primary/5"
      )}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      draggable={true}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Drag Handle */}
      <div className="absolute left-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
        <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab active:cursor-grabbing" />
      </div>

      <div className="flex items-start justify-between mb-2 ml-6">
        <h3 className="text-sm font-semibold text-foreground line-clamp-1">
          {highlightText(prompt.title, searchQuery)}
        </h3>
        {folderName && (
          <Badge variant="secondary" className="ml-2 text-xs">
            {highlightText(folderName, searchQuery)}
          </Badge>
        )}
      </div>

      {prompt.description && (
        <p className="text-xs text-muted-foreground mb-3 ml-6 line-clamp-2 leading-relaxed">
          {highlightText(prompt.description, searchQuery)}
        </p>
      )}

      <div className="flex items-center justify-between ml-6">
        <div className="text-xs text-muted-foreground">
          {prompt.usageCount > 0 && (
            <span>使用 {prompt.usageCount} 次</span>
          )}
        </div>
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={(e) => {
              e.stopPropagation();
              if (onEdit) {
                onEdit(prompt);
              }
            }}
            title="编辑"
          >
            <Edit className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-destructive hover:text-destructive"
            onClick={(e) => {
              e.stopPropagation();
              if (onDelete && window.confirm('确定要删除这个 Prompt 吗？')) {
                onDelete(prompt.id);
              }
            }}
            title="删除"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
});

PromptItem.displayName = 'PromptItem';

export default PromptItem;
